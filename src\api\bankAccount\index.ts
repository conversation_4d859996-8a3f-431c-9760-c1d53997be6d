import request from '@/utils/http'
import config from '@/utils/config'
import { BankInfoType } from './indexType'

// 获取银行卡列表
export function getBankListApi() {
  return request({
    url: `${config.baseURL}/index/withdrawal`,
    method: 'get'
  })
}

// 添加银行卡
export function addBankApi(data: BankInfoType) {
  return request({
    url: `${config.baseURL}/index/setUserInfo`,
    method: 'post',
    data
  })
}

// 添加银行卡
export function delBankApi(id: number | string) {
  return request({
    url: `${config.baseURL}/index/bankcardDelete`,
    method: 'get',
    params: {
      id
    }
  })
}
