export interface getGupiaoType {
  code: string
}

export interface getTickerKType {
  id: number
  kline_type: number
}

export interface idType {
  id: string
}

export interface buyGupiaoType {
  buytype: number
  fangxiang: string
  price: number
  pro_id: number
  shuliang: number
  ganggan_ratio: number
}

export interface getJiaoyiDetailType {
  id: string
  type: string
}

export interface shengouType {
  id: string
  number: number
  price: string
}

export interface buyDadanType {
  monitoring_id: string
  num: number
}

export interface buyHongliType {
  dividend_id: number
  num: number
}

export interface buyGendanType {
  follow_id: number
  money: number
}

export interface sellGendanType {
  follow_id: number
}

export interface rongziType {
  money: any
  name: any
  card: any
  address: any
  phone: any
  mail: any
  income: any
  credit: any
  qualifications: any
}
