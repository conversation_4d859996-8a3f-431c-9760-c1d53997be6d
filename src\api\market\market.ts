import request from '@/utils/http'
import config from '@/utils/config'
import { getGupiaoListType, idType } from './marketType'

// 股票列表
export function getGupiaoListApi(params: getGupiaoListType) {
  return request({
    url: `${config.baseURL}/product/get_stock_list`,
    method: 'get',
    params
  })
}

// 获取收藏股票列表
export function getZixuanlApi() {
  return request({
    url: `${config.baseURL}/index/getZixuan`,
    method: 'get'
  })
}

// 指数列表
export function getZhishuApi() {
  return request({
    url: `${config.baseURL}/product/get_zhishu_list`,
    method: 'get'
  })
}

// 指数详情
export function getZhishuDetailApi(params: idType) {
  return request({
    url: `${config.baseURL}/product/getIndexDetail`,
    method: 'get',
    params
  })
}
