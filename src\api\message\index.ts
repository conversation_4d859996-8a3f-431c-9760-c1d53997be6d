import request from '@/utils/http'
import config from '@/utils/config'
// import { userInfoParameterType } from './indexType'

export function getUserMessageApi(params) {
  return request({
    url: `${config.baseURL}/index/get_user_message`,
    method: 'get',
    params
  })
}
export function getUserMessageDetailApi() {
  return request({
    url: `${config.baseURL}/index/get_user_message_detail`,
    method: 'get'
  })
}
