import request from '@/utils/http'
import config from '@/utils/config'
import { RealNameType } from './indexType'

// 获取用户实名状态
export function getRealnameApi() {
  return request({
    url: `${config.baseURL}/user/getRealname`,
    method: 'get'
  })
}

export function subRealnameApi(data: RealNameType) {
  return request({
    url: `${config.baseURL}/user/subRealname`,
    method: 'post',
    data
  })
}
