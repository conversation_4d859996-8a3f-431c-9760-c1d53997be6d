import request from '@/utils/http'
import config from '@/utils/config'
import { paramsType } from './recordType'

// 获取新闻列表
export function getNewsApi(params: paramsType) {
  return request({
    url: `${config.baseURL}/index/getNews`,
    method: 'get',
    params
  })
}

// 获取新闻详情
export function getNewDetailApi(id: number | string) {
  return request({
    url: `${config.baseURL}/index/newsDetail`,
    method: 'get',
    params: { id }
  })
}
