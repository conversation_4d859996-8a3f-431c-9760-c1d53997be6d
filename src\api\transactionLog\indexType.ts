export interface LiuShuiType {
  type: number
  page: number
}

export interface JyType {
  detailed: string
  createtime: string
  agent_id: number
  id: number
  money: number | string
  order_sn: string
  product_name: string
  status: number | string
  updatetime: string | null
  user_id: number
  name: string
  gpCode: string | number
  gpName: string
}

export interface CzType {
  beizhu: ?string
  createtime: string
  id: number
  money: number | string
  order_sn: string
  remark: string
  status: number | string
  type: string | number
}
