import request from '@/utils/http'

import config from '@/utils/config'
import { userInfoParameterType } from './indexType'

export function getUserInfoApi(params: userInfoParameterType) {
  return request({
    url: `${config.baseURL}/index/getUserInfo`,
    method: 'get',
    params
  })
}

// 获取客服地址
export function getCzhiurlApi() {
  return request({
    url: `${config.baseURL}/index/getCzhiurl`,
    method: 'get'
  })
}

// 获取客服地址
export function getWithdrawal(user_id: number | null) {
  return request({
    url: `${config.baseURL}/index/withdrawal`,
    method: 'get',
    params: {
      user_id
    }
  })
}
