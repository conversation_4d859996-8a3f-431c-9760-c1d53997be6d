import { createI18n } from 'vue-i18n'
import jp from './ja'
import cn from './cn'
import en from './en'
import sp from './sp'
import fr from './fr'

// 引入的不同语言文件
const messages = {
  jp,
  cn,
  en,
  sp,
  fr
}
// uni.setStorageSync('locale', 'jp')
// console.log(uni.getStorageSync('locale'), 'localelocalelocale')
let locale = 'jp'
if (uni.getStorageSync('locale') === '') {
  uni.setStorageSync('locale', 'jp')
} else {
  locale = uni.getStorageSync('locale')
}

// 这个类型可以自己配置，毕竟每个人做的都不一样
const i18n: any = createI18n({
  legacy: false, // 使用 Composition API 模式，则需要将其设置为false
  globalInjection: true, // 全局生效$t
  locale: locale, // 默认cn翻译
  messages // ES6解构
})

export default i18n
