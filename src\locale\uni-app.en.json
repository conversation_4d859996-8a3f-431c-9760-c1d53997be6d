{"common": {"uni.app.quit": "Press again to exit the app", "uni.async.error": "Timeout on server connection, tap the screen to try again", "uni.showActionSheet.cancel": "Cancel", "uni.showToast.unpaired": "Please note that showToast and hideToast must be used in pairs", "uni.showLoading.unpaired": "Please note that showLoading and hideLoading must be used in pairs", "uni.showModal.cancel": "Cancel", "uni.showModal.confirm": "Confirm", "uni.chooseImage.cancel": "Cancel", "uni.chooseImage.sourceType.album": "Select from album", "uni.chooseImage.sourceType.camera": "Shoot", "uni.chooseVideo.cancel": "Cancel", "uni.chooseVideo.sourceType.album": "Select from album", "uni.chooseVideo.sourceType.camera": "Shoot", "uni.previewImage.cancel": "Cancel", "uni.previewImage.button.save": "Save image", "uni.previewImage.save.success": "Save image to album successfully", "uni.previewImage.save.fail": "Save image to album failed", "uni.setClipboardData.success": "Content copied", "uni.scanCode.title": "Scan code", "uni.scanCode.album": "Album", "uni.scanCode.fail": "Recognition failed", "uni.scanCode.flash.on": "Tap to illuminate", "uni.scanCode.flash.off": "Tap to close", "uni.startSoterAuthentication.authContent": "Fingerprint recognition in progress...", "uni.picker.done": "Done", "uni.picker.cancel": "Cancel", "uni.video.danmu": "Bullet screen", "uni.video.volume": "Volume", "uni.button.feedback.title": "Problem feedback", "uni.button.feedback.send": "Send"}, "ios": {}, "android": {}}