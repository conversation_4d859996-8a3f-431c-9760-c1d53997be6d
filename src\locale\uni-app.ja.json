{"common": {"uni.app.quit": "もう一度押すとアプリを終了します", "uni.async.error": "接続がタイムアウトしました。画面をタップして再試行してください。", "uni.showActionSheet.cancel": "キャンセル", "uni.showToast.unpaired": "showToastとhideToastはペアで使用してください", "uni.showLoading.unpaired": "showLoadingとhideLoadingはペアで使用してください", "uni.showModal.cancel": "キャンセル", "uni.showModal.confirm": "確認", "uni.chooseImage.cancel": "キャンセル", "uni.chooseImage.sourceType.album": "アルバムから選択", "uni.chooseImage.sourceType.camera": "撮影", "uni.chooseVideo.cancel": "キャンセル", "uni.chooseVideo.sourceType.album": "アルバムから選択", "uni.chooseVideo.sourceType.camera": "撮影", "uni.previewImage.cancel": "キャンセル", "uni.previewImage.button.save": "画像を保存", "uni.previewImage.save.success": "アルバムに画像を保存しました", "uni.previewImage.save.fail": "アルバムに画像を保存できませんでした", "uni.setClipboardData.success": "コンテンツをコピーしました", "uni.scanCode.title": "コードをスキャン", "uni.scanCode.album": "アルバム", "uni.scanCode.fail": "認識に失敗しました", "uni.scanCode.flash.on": "タップして点灯します", "uni.scanCode.flash.off": "タップして閉じます", "uni.startSoterAuthentication.authContent": "指紋認識を実行しています…", "uni.picker.done": "完了", "uni.picker.cancel": "キャンセル", "uni.video.danmu": "バレットスクリーン", "uni.video.volume": "音量", "uni.button.feedback.title": "問題に関するフィードバック", "uni.button.feedback.send": "送信"}, "ios": {}, "android": {}}