{"common": {"uni.app.quit": "再按一次退出应用", "uni.async.error": "连接服务器超时，点击屏幕重试", "uni.showActionSheet.cancel": "取消", "uni.showToast.unpaired": "请注意 showToast 与 hideToast 必须配对使用", "uni.showLoading.unpaired": "请注意 showLoading 与 hideLoading 必须配对使用", "uni.showModal.cancel": "取消", "uni.showModal.confirm": "确定", "uni.chooseImage.cancel": "取消", "uni.chooseImage.sourceType.album": "从相册选择", "uni.chooseImage.sourceType.camera": "拍摄", "uni.chooseVideo.cancel": "取消", "uni.chooseVideo.sourceType.album": "从相册选择", "uni.chooseVideo.sourceType.camera": "拍摄", "uni.previewImage.cancel": "取消", "uni.previewImage.button.save": "保存图像", "uni.previewImage.save.success": "保存图像到相册成功", "uni.previewImage.save.fail": "保存图像到相册失败", "uni.setClipboardData.success": "内容已复制", "uni.scanCode.title": "扫码", "uni.scanCode.album": "相册", "uni.scanCode.fail": "识别失败", "uni.scanCode.flash.on": "轻触照亮", "uni.scanCode.flash.off": "轻触关闭", "uni.startSoterAuthentication.authContent": "指纹识别中...", "uni.picker.done": "完成", "uni.picker.cancel": "取消", "uni.video.danmu": "弹幕", "uni.video.volume": "音量", "uni.button.feedback.title": "问题反馈", "uni.button.feedback.send": "发送"}, "ios": {}, "android": {}}