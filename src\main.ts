import { createSSRApp } from 'vue'
import App from './App.vue'
// import '@/style/base/normalize/normalize.css'
import '@/style/index.scss'
import i18n from '@/langurage/index'
import NotData from '@/components/notData/index.vue'
import Navigater from '@/components/navigator/navigator.vue'

import { Switch, Dialog, Icon, Button, Field, Search, Loading, Popup, Stepper, Cell, CellGroup, Picker, Image as VanImage, Badge, TextEllipsis } from 'vant'

import 'vant/lib/index.css'
import 'animate.css/animate.min.css'
import './style/tailwind.scss'

import { createPinia } from 'pinia'

const pinia = createPinia()

// uni.setStorageSync('locale', 'jp')

export function createApp() {
  const app = createSSRApp(App)
    .use(pinia)
    .use(i18n)
    .use(Icon)
    .use(Button)
    .use(Field)
    .use(Loading)
    .use(Search)
    .use(Popup)
    .use(Stepper)
    .use(Cell)
    .use(CellGroup)
    .use(Picker)
    .use(VanImage)
    .use(Badge)
    .use(TextEllipsis)
    .use(Dialog)
    .use(Switch)
  app.component('NotData', NotData)
  app.component('Navigater', Navigater)
  return {
    app
  }
}
