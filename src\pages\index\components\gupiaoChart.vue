<template>
  <view id="indexChart" style="width: 100%; height: 154.206px" :style="{ height: height }"></view>
</template>

<script lang="ts" setup>
import { init, dispose } from 'klinecharts'
import { onMounted, onUnmounted, watch } from 'vue'
const props = defineProps({
  value: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '154px'
  }
})
let chart: any = null
watch(
  () => props.value,
  (newData) => {
    chart?.applyNewData(newData)
  },
  { deep: true }
)
const chartOption = {
  candle: {
    type: 'candle_solid',
    bar: {
      upColor: '#00D432',
      downColor: '#F53F3F',
      noChangeColor: '#888888',
      upBorderColor: '#00D432',
      downBorderColor: '#F53F3F',
      noChangeBorderColor: '#888888',
      upWickColor: '#00D432',
      downWickColor: '#F53F3F',
      noChangeWickColor: '#888888'
    },
    tooltip: {
      showRule: 'none'
    }
  },
  grid: {
    show: true,
    horizontal: {
      show: false // ❌ 关闭 X 轴方向的虚线（横向）
    },
    vertical: {
      show: false, // ✅ 保留 Y 轴方向虚线
      size: 1,
      color: '#c9c9c9',
      style: 'dashed',
      dashedValue: [2, 2]
    },
    border: {
      top: false,
      bottom: false, // ❌ 去掉底部边框
      left: false,
      right: false // ❌ 去掉右侧边框
    }
  },
  xAxis: {
    tickText: {
      color: '#AEBAC9'
    }
  },
  yAxis: {
    tickText: {
      color: '#AEBAC9'
    }
  }
}

onMounted(() => {
  chart = init('indexChart')
  chart.setStyles(chartOption)
})

onUnmounted(() => {
  dispose('chart')
})
</script>
<style scoped lang="scss"></style>
