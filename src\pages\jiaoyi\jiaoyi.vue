<template>
  <view class="header">
    <Navigator :is-show-back="false" :title="t('tabbar.jiaoyi')" />
  </view>
  <div class="container">
    <div class="wrap">
      <view id="button_wrap" class="button_wrap">
        <view class="button" :class="{ active: pageType === 0 }" @click="changePageType(0)">{{ t('jiaoyi.weishixiansunyi') }}</view>
        <view class="button" :class="{ active: pageType === 1 }" @click="changePageType(1)">{{ t('jiaoyi.yishixiansunyi') }}</view>
        <view class="button" :class="{ active: pageType === 2 }" @click="changePageType(2)">{{ t('jiaoyi.sunyiheji3') }}</view>
      </view>
      <view class="content" :style="{ height: `calc(${store.pageHeight} - 11.2rem )` }">
        <view class="gupiao_list">
          <view v-show="pageType === 0">
            <NotData v-if="!weishixianList.length" />
            <view v-for="(item, index) in weishixianList" :key="index" class="gupiao1" @click="goPage(`/subPackages/jiaoyi/jiaoyiDetail/jiaoyiDetail?id=${item.id}&ismai=1`)">
              <div class="head">
                <view class="icon_green">{{ getStatus(item) }}</view>
                <div class="icon_wrap">
                  <van-icon name="arrow" color="#afafaf"></van-icon>
                </div>
              </div>
              <div class="info">
                <div class="left">
                  <div class="title">{{ item?.product?.name }}</div>
                  <div class="daima">{{ item?.product?.shuzidaima }}</div>
                </div>
                <div class="right">
                  <div class="price">
                    <view class="label">{{ t('jiaoyi.xianjia') }}：</view>
                    <view class="value" :class="getColor2(item?.yingkui)">{{ qfw(item?.product?.price) }}</view>
                  </div>
                </div>
              </div>
              <div class="bottom">
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.chigushu') }}</view>
                    <view class="value">{{ item.shuliang }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.xianzaizongjia') }}</view>
                    <view class="value">{{ qfw(item.shizhi) }}</view>
                  </div>
                </div>
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.goumaijiage') }}</view>
                    <view class="value">{{ qfw(item.price) }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.yugusunyi') }}</view>
                    <view class="value" :class="getColor2(item?.yingkui)">{{ changeMoney(item?.yingkui) }}</view>
                  </div>
                </div>
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.goumaizongjia') }}</view>
                    <view class="value">{{ qfw(((item.price * item.shuliang) / Number(item.ganggan_ratio)).toFixed(1)) }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.sunyilv') }}</view>
                    <view class="value" :class="getColor2(item?.yingkui)">{{ item?.yisun }}</view>
                  </div>
                </div>
                <view v-if="item.status !== '4'" class="button" @click.stop="openSell(item)">{{ t('jiaoyi.maichu') }}</view>
                <view v-if="item.status === '4'" class="button" @click.stop="openCancel(item)">{{ t('jiaoyi.chedan') }}</view>
              </div>
            </view>
          </view>

          <view v-show="pageType === 1">
            <NotData v-if="!yishixianList.length" />
            <view v-for="(item, index) in yishixianList" :key="index" class="gupiao1" @click="goPage(`/subPackages/jiaoyi/jiaoyiDetail/jiaoyiDetail?id=${item.id}&ismai=0`)">
              <div class="head">
                <view class="icon_primary">{{ getStatus(item) }}</view>
                <div class="icon_wrap">
                  <van-icon name="arrow" color="#afafaf"></van-icon>
                </div>
              </div>
              <div class="info">
                <div class="left">
                  <div class="title">{{ item?.product?.name }}</div>
                  <div class="daima">{{ item?.product?.shuzidaima }}</div>
                </div>
                <div class="right">
                  <div class="price">
                    <view class="label">{{ t('jiaoyi.xianzaijiage') }}：</view>
                    <view class="value" :class="getColor2(item?.yingkui)">{{ qfw(item?.sellprice) }}</view>
                  </div>
                </div>
              </div>
              <div class="bottom">
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.chigushu') }}</view>
                    <view class="value">{{ item.shuliang }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.xianzaizongjia') }}</view>
                    <view class="value">{{ qfw(item.shizhi) }}</view>
                  </div>
                </div>
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.goumaijiage') }}</view>
                    <view class="value">{{ qfw(item.price) }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.yugusunyi2') }}</view>
                    <view class="value" :class="getColor2(item?.yingkui)">{{ changeMoney(item?.yingkui) }}</view>
                  </div>
                </div>
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.goumaizongjia') }}</view>
                    <view class="value">{{ qfw(((item.price * item.shuliang) / Number(item.ganggan_ratio)).toFixed(1)) }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.sunyilv') }}</view>
                    <view class="value" :class="getColor2(item?.yingkui)">{{ item?.yisun }}</view>
                  </div>
                </div>
              </div>
            </view>
          </view>
          <view v-show="pageType === 2">
            <NotData v-if="!apply.length" />
            <view v-for="(item, index) in apply" :key="index" class="gupiao1 gupiao2">
              <div class="info" style="margin-top: 0">
                <div class="left">
                  <div class="title">{{ item?.product?.name }}</div>
                  <div class="daima">{{ item?.product?.shuzidaima }}</div>
                </div>
                <div class="right head">
                  <view class="icon_green2">{{ getText(item) }}</view>
                </div>
              </div>
              <div class="bottom">
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.jiage') }}</view>
                    <view class="value">{{ changeMoney(item.price) }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.zhognqianshuliang') }}</view>
                    <view class="value">{{ item.zhongqianshu }}</view>
                  </div>
                </div>
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.shenqingliang') }}</view>
                    <view class="value">{{ item.shengoushuliang }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.renjiaogushu') }}</view>
                    <view class="value">{{ item.renjiaonum || 0 }}</view>
                  </div>
                </div>
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.chouqianri') }}</view>
                    <view class="value">{{ item?.product?.chouqiandate }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.txt1') }}</view>
                    <view class="value">{{ changeMoney(item.yingrenjiao) }}</view>
                  </div>
                </div>
                <div class="row">
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.faquanri') }}</view>
                    <view class="value">{{ item?.product?.faquan_date }}</view>
                  </div>
                  <div class="flex">
                    <view class="label">{{ t('jiaoyi.txt2') }}</view>
                    <view class="value">{{ changeMoney(item.yirenjiao) }}</view>
                  </div>
                </div>
              </div>
            </view>
          </view>
        </view>
      </view>
    </div>
  </div>
  <CustomTabbar :id="2" />
  <van-popup v-model:show="gupiaoWindowShow" class="buy_window" v-bind="windowOptions">
    <view class="title">{{ t('jiaoyi.tip6') }}</view>
    <view class="title2">{{ windowInfo }}</view>

    <div class="button_group">
      <view class="button cancel" @click="gupiaoWindowShow = false">{{ t('jiaoyi.tip8') }}</view>
      <div class="button" @click="windowConfirm">{{ t('jiaoyi.tip9') }}</div>
    </div>
    <van-icon class="close" name="cross" color="#fff" size="1.2rem" @click="gupiaoWindowShow = false" />
  </van-popup>
</template>
<script setup lang="ts">
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'
import { useI18n } from 'vue-i18n'
import { getJiaoyiListApi, getUserMoneyApi, sellGupiaoApi, cancelOrdeApi } from '@/api/index/index'
import { useCounterStore } from '@/store/store'
import { onHide, onLoad, onShow } from '@dcloudio/uni-app'
import Navigator from '@/components/navigator/navigator.vue'
import { changeMoney, goPage, getColor2 } from '@/common/common'
import * as echarts from 'echarts'
import { computed, onMounted, ref } from 'vue'
import { showLoadingToast, showToast } from 'vant'
const { t } = useI18n()
// 页面基础配置
const store = useCounterStore()

const gupiaoWindowShow = ref(false)
const windowOptions = {
  position: 'bottom',
  'close-on-click-overlay': true,
  'safe-area-inset-bottom': true,
  round: true
}

onLoad(async () => {
  await getJiaoyiList()

  InitChart()
})
const butth = ref('')
onMounted(() => {
  butth.value = getH('button_wrap')
})

const getH = (id) => {
  const el = document.getElementById(id)
  return getComputedStyle(el).height
}

const tip1 = computed(() => t('jiaoyi.tip1'))
const tip2 = computed(() => t('jiaoyi.tip2'))
const tip3 = computed(() => t('jiaoyi.tip3'))
const tip4 = computed(() => t('jiaoyi.tip4'))
const tip5 = computed(() => t('jiaoyi.tip5'))

const statusText = [tip1, tip2, tip3, tip4, tip5]

const getStatus = (item) => {
  // 1=持仓中,2=已平仓,3=平仓中,4=挂单中,5=已撤单',
  return statusText[item.status * 1 - 1].value
}

const getText = (item) => {
  if (item.status === '0') {
    return t('jiaoyi.txt3')
  } else if (item.status === '1' && item.is_examine === 0) {
    return t('jiaoyi.txt4')
  } else if (item.status === '1' && item.is_examine === 1) {
    return t('jiaoyi.txt5')
  } else if (item.status === '1' && item.is_examine === 2) {
    return t('jiaoyi.txt6')
  } else if (item.status === '2') {
    return t('jiaoyi.txt6')
  }
}

let intervalId: ReturnType<typeof setInterval> | null = null
onShow(() => {
  getUserMoney()
  getJiaoyiList()
  if (intervalId === null) {
    intervalId = setInterval(() => {
      getJiaoyiList()
    }, 8000)
  }
})

onHide(() => {
  console.log(intervalId, '定时器ID清除')
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = null
  }
})
// 获取交易列表
const weishixianList = ref([])
const yishixianList = ref([])
const pageType = ref(0)
const jiaoyiInfo = ref({
  my_losses: {}
})

// 新股抽签
const apply = ref([])
const getJiaoyiList = async () => {
  const res = await getJiaoyiListApi()
  weishixianList.value = res.data.already
  apply.value = res.data.apply
  yishixianList.value = res.data.future
  jiaoyiInfo.value = res.data
}
const changePageType = (e: number) => {
  pageType.value = e
  const data1 = [{ value: userMoneyInfo.value?.money || 0 }, { value: jiaoyiInfo.value?.my_losses?.count_market || 0 }, { value: jiaoyiInfo.value?.my_losses?.count_losses || 0 }]
  const data2 = [{ value: userMoneyInfo.value?.money || 0 }, { value: jiaoyiInfo.value?.my_realized_losses?.count_market || 0 }, { value: jiaoyiInfo.value?.my_realized_losses?.count_losses || 0 }]
  const option = {
    series: {
      data: e === 0 ? data1 : data2
    }
  }
  myChart.value.setOption(option)
}

// 获取用户余额
const userMoneyInfo = ref({
  total: 0,
  total2: 0
})
// const isHidden = ref(true)
const getUserMoney = async () => {
  const res = await getUserMoneyApi()
  res.data.total = (Number(jiaoyiInfo.value?.my_losses?.count_market || 0) + Number(jiaoyiInfo.value?.my_losses?.count_losses || 0) + Number(res.data.money || 0)).toFixed(1)
  res.data.tota2 = (Number(jiaoyiInfo.value?.my_realized_losses?.count_market || 0) + Number(jiaoyiInfo.value?.my_realized_losses?.count_losses || 0) + Number(res.data.money || 0)).toFixed(1)
  userMoneyInfo.value = res.data
}

// 图表
const myChart = ref(null)
const InitChart = () => {
  myChart.value = echarts.init(document.getElementById('charts'))
  const option = {
    series: {
      type: 'pie',
      radius: '80%',
      label: {
        show: false,
        position: 'center'
      },
      avoidLabelOverlap: false,
      color: ['#C850F0', '#50F0AA', '#50AFF0'],
      data: [{ value: userMoneyInfo.value?.money || 0 }, { value: jiaoyiInfo.value?.my_losses?.count_market || 0 }, { value: jiaoyiInfo.value?.my_losses?.count_losses || 0 }]
    }
  }
  option && myChart.value.setOption(option)
  console.log(myChart)
}

const qfw = (num) => {
  return Number(num).toLocaleString()
}

const windowType = ref('sell')
const windowInfo = ref('')
const windowId = ref('')
// 卖出
const openSell = (item) => {
  windowId.value = item.id
  windowType.value = 'sell'
  windowInfo.value = t('jiaoyi.tip7')
  gupiaoWindowShow.value = true
}
const openCancel = (item) => {
  windowId.value = item.id
  windowType.value = 'cancel'
  windowInfo.value = t('jiaoyi.tip10')
  gupiaoWindowShow.value = true
}
const windowConfirm = async () => {
  showLoadingToast({ forbidClick: true, duration: 0 })
  gupiaoWindowShow.value = false
  if (windowType.value === 'sell') {
    const res = await sellGupiaoApi({ id: windowId.value, type: '1' })
    getUserMoney()
    showToast({
      message: res.data.msg,
      duration: 2000
    })
    getJiaoyiList()
  }
  // 撤单
  else {
    const res = await cancelOrdeApi({ id: windowId.value, type: '1' })
    getUserMoney()
    showToast({
      message: res.msg,
      duration: 2000
    })
    getJiaoyiList()
  }
}
</script>
<style lang="scss" scoped>
.red {
  // background: red !important;
}
.green {
  // background: #34e607 !important;
}
.txt-green {
  color: $color-green !important;
}

.txt-red {
  color: $color-red !important;
}

.text-primary {
  color: #fff;
}
.xiaoxi {
  width: 1.8rem;
  position: relative;
  top: -0.2rem;
  right: -0.2rem;
  // padding: 0.1rem;
}
::v-deep .van-badge {
  position: absolute;
  top: 0.35rem;
  right: 0.35rem;
}
.container {
  padding: 1rem 0.63rem 0;
}
.button_wrap {
  height: 2.13rem;
  margin: 0 0.31rem 0.5rem;
  display: flex;
  justify-content: space-around;
  gap: 0.5rem;
  .button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 6.875rem;
    padding: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 1rem;
    background: transparent;
    border: 0.05rem solid $color-primary;
    color: $color-white;
  }
  .active {
    background-color: $color-primary;
    border: 0;
    color: $color-white;
    font-weight: 500;
  }
}
.content {
  overflow-y: auto;

  .gupiao_list {
    padding: 0 0.3rem 1rem;
    .icon {
      background: #0575e6;
      padding: 0.14rem 0.44rem;
      border-radius: 0.31rem;
      color: #fff;
      margin-left: 1rem;
      white-space: nowrap;
    }
    .icon_primary {
      background: $uni-color-primary;
      padding: 0.25rem 0.63rem;
      width: 6.25rem;
      font-size: $uni-font-size-lg;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      word-break: break-all;
      background: linear-gradient(90deg, #fd803950 0%, rgba(253, 128, 57, 0) 100%);
      color: $color-red;
    }
    .icon_o {
      background: #fff;
      padding: 0.18rem 1rem;
      border-radius: 0.31rem;
      color: $uni-color-primary;
      border: 0.03rem solid $uni-color-primary;
      margin-left: 1rem;
      font-size: $uni-font-size-lg;
    }
    .gupiao1 {
      background: #ffffff10;
      border: 0.06rem solid #ffffff16;
      border-radius: 0.63rem;
      padding: 0.94rem 0 0.38rem;
      margin-bottom: 0.31rem;
      box-shadow: 0rem 0 0.23rem 0.1rem rgba(0, 0, 0, 0.1);
      .head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1.25rem;
        .icon_green {
          background: linear-gradient(90deg, #00816150 0%, rgba(177, 181, 186, 0) 100%);
          width: 6.25rem;
          color: $color-green;
          padding: 0.25rem 0.63rem;
          font-size: $uni-font-size-lg;
          color: $color-green;
        }
        .icon_green2 {
          background: $color-primary;
          border-radius: 0.19rem;
          color: #fff;
          padding: 0.18rem 1rem;
          border-radius: 0.31rem;
          font-size: $uni-font-size-lg;
        }
        .icon_wrap {
          flex: 1;
          text-align: right;
        }
        .tag {
          height: 1.25rem;
          line-height: 1.25rem;
          border-radius: 0.19rem;
          text-align: center;

          background: #3b363f;
          font-size: 0.81rem;
          color: #fff;
          padding: 0 0.5rem;
        }
        .red_tag {
          background: $color-red;
          color: #fff;
        }
        .green_tag {
          background: $color-green;
          color: #fff;
        }
      }
      .info {
        margin: 0.88rem 0 0;
        padding: 0 0.75rem;
        display: flex;
        justify-content: space-between;
        .price {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          .label {
            color: $color-white;
            font-size: 0.81rem;
          }
          .value {
            font-size: 1.13rem;
          }
        }
        .title {
          font-size: 0.94rem;
          font-weight: 500;
          color: $color-white;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 10rem;
        }
        .daima {
          font-size: 0.81rem;
          color: $color-gray;
        }
      }
      .bottom {
        padding: 0 0.75rem;
        .row {
          justify-content: space-between;
          gap: 1rem;
          .flex {
            display: flex;
            justify-content: space-between;
            padding: 0.1rem 0;
          }
          .label {
            color: $color-gray;
            font-size: 0.81rem;
          }
          .value {
            color: $color-white;
            font-size: 0.81rem;
            width: 5rem;
            text-align: right;
          }
        }
        .button {
          width: 19.38rem;
          height: 1.81rem;
          line-height: 1.81rem;
          border-radius: 0.5rem;
          text-align: center;
          font-size: 0.81rem;
          color: #fff;
          margin: 0.5rem auto 0;
          background: $color-primary;
          font-weight: 500;
        }
      }
    }
    .gupiao2 {
      .flex {
        flex: 1;
        justify-content: space-between !important;
        .label {
          width: auto !important;
        }
        .value {
          width: auto !important;
        }
      }
    }
  }
}
.buy_window {
  padding: 0 0.81rem 1.75rem 0.81rem;
  background: #ffffff10;
  backdrop-filter: blur(10px);
  .title {
    text-align: center;
    padding: 0.8rem 0 0.5rem;
    font-size: 1rem;
    color: $color-white;
  }
  .title2 {
    text-align: center;
    padding: 0.8rem 0 0.5rem;
    font-size: 0.88rem;
    color: $color-gray;
  }
  .button_group {
    display: flex;
    gap: 1.56rem;
  }
  .button {
    height: 2.75rem;
    line-height: 2.75rem;
    width: 100%;
    text-align: center;
    background-color: $color-primary;
    color: $color-black;
    font-size: 0.88rem;
    margin-top: 1rem;
    border-radius: 0.94rem;
    font-weight: 500;
    color: #fff;
  }
  .cancel {
    background: #f76961;
  }
  .close {
    position: absolute;
    width: 2.75rem;
    height: 2.75rem;
    padding: 1rem;
    right: 0rem;
    top: 0rem;
  }
}
</style>
