export interface userInfoType {
  birthday: any
  avatar: string
  bio: string
  change_mini_price: string
  createtime: number
  email: string
  gender: number
  group_id: number
  id: number
  is_auth: number
  joinip: string
  jointime: number
  level: number
  loginfailure: number
  loginip: string
  logintime: number
  maxsuccessions: number
  mobile: string
  money: string | number
  nickname: string
  password: string
  prevtime: string | number
  salt: string
  score: number
  shizhi: string
  status: string
  successions: number
  sxf: string
  sxfzdi: string
  token: string
  total: string
  true_name: string
  updatetime: number
  username: string
  verification: string
}
