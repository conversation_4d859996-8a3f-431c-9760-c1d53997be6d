<script setup lang="ts">
import { Ref, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { BankInfoType } from '@/api/bankAccount/indexType'
import { addBankApi } from '@/api/bankAccount'
import { showLoadingToast, showSuccessToast } from 'vant'
const form: Ref<BankInfoType> = ref({
  shiming_name: '',
  bank_card: '',
  bank_address: '',
  bank_name: '',
  bank_code: ''
})
const formList = [
  {
    label: 'account.zhanghaoming',
    dataName: 'shiming_name',
    msgName: 'checkMsg.shuruxinming'
  },
  {
    label: 'account.zhanghao',
    dataName: 'bank_card',
    msgName: 'checkMsg.shuruzhanghao'
  },
  {
    label: 'account.yinghangming',
    dataName: 'bank_name',
    msgName: 'checkMsg.shuruyinhangming'
  },
  {
    label: 'account.fenhangmingceng',
    dataName: 'bank_address',
    msgName: 'checkMsg.shurufenhangming'
  },
  {
    label: 'account.fenhangbianhap',
    dataName: 'bank_code',
    msgName: 'checkMsg.shurufenhanghaomao'
  }
]
const { t } = useI18n()

// 提交表单
const submitFn = async () => {
  showLoadingToast({ forbidClick: true, duration: 0 })
  const res = await addBankApi(form.value)
  if (res.code === 1) {
    showSuccessToast(res.data.msg)
    uni.redirectTo({
      url: '/subPackages/bankAccount/bankAccount'
    })
  }
}
</script>

<template>
  <Navigater :title="t('account.cunquzhanghaodengji')"></Navigater>

  <view class="Box">
    <view class="box-form-card">
      <view v-for="(item, i) in formList" :key="i" class="box-form-item">
        <view class="box-form-item-label"> &nbsp;&nbsp;{{ t(item.label) }}</view>
        <van-field v-model="form[item.dataName]" :placeholder="t(item.msgName)" />
      </view>
      <van-button class="btn" type="primary" @click="submitFn">{{ t('account.cunquzhanghaodengjibtn') }}</van-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
::v-deep .van-field__control {
  height: 2rem;
  border: none;
  font-weight: 400;
  font-size: 0.9375rem;
  color: $color-white;
  &::placeholder {
    color: $color-gray;
  }
}
::v-deep .van-field {
  height: 3.375rem;
  background: transparent;
}
::v-deep .van-field__body {
  height: 3.375rem;
  background: #ffffff10;
  border: 0.06rem solid #ffffff16;
  border-radius: 0.5rem;
  padding: 1.0938rem 0.9375rem 0.9688rem 1.25rem;
  .van-field__right-icon {
    padding: 0;
    margin-right: 0.625rem;
    .van-icon__image {
      width: 1.19rem;
    }
  }
}

::v-deep .van-button {
  width: 100%;
  height: 2.75rem;
  background: $color-primary;
  border-radius: 0.5rem;
  font-size: 0.9375rem;
  font-weight: 500;
  margin: 2.5rem 0 0;
}

::v-deep .van-button__text {
  font-size: 0.9375rem;
  font-weight: 500;
}

::v-deep .van-field {
  padding: 0;
}

.Box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 1rem;
  .box-form-card {
    width: 100%;
    padding: 0.94rem 0;

    .btn {
      margin-top: 1.25rem;
      height: 3.06rem;
      border-radius: 1.13rem;
    }

    .box-form-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 1.2rem;
      .box-form-item-label {
        margin-bottom: 0.5938rem;
        font-weight: 500;
        font-size: $uni-font-size-2;
        color: $color-white;
        line-height: 0.75rem;
      }
    }
  }

  .box-top {
    width: 11.4688rem;
    height: 11.4688rem;
    background: #ffffff;
    border-radius: 50%;

    font-weight: 300;
    font-size: 2.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
