<template>
  <Navigater :title="t('user.yinhangzhanghu')" />

  <view class="inlet" @click="goPage">
    <van-icon name="plus" size="1.7rem" color="#EE2560"></van-icon>
    <div class="label">{{ t('account.cunquzhanghaodengji') }}</div>
  </view>
  <scroll-view scroll-y :style="{ height: `calc(${store.pageHeight} - 9.5313rem)` }">
    <view class="account-card-list">
      <NotData v-if="!bankList.length" />

      <div v-for="(item, index) in bankList" :key="index" class="card">
        <view class="name">{{ item.bank_name }}({{ item.shiming_name }})</view>
        <view class="num">{{ item.bank_num }}</view>

        <van-icon class="delete" name="delete-o" color="#fff" size="20" @click="delBank(item.id)" />
      </div>
    </view>
  </scroll-view>
</template>
<script setup lang="ts">
import { getBankListApi, delBankApi } from '@/api/bankAccount'
import { BankInfoType } from '@/api/bankAccount/indexType'
import { useCounterStore } from '@/store/store'
import { useI18n } from 'vue-i18n'
import { Ref, ref } from 'vue'
import { showSuccessToast } from 'vant'
import { onShow } from '@dcloudio/uni-app'
const { t } = useI18n()
// 页面基础配置
const store = useCounterStore()

type BankListType = [BankInfoType | never]

const bankList: Ref<BankListType> = ref([])

const delBank = async (id: number | string) => {
  const res = await delBankApi(id)

  if (res.code === 1) {
    showSuccessToast(res.data.msg)
    getBankLsit()
  }
}

const getBankLsit = async () => {
  const res = await getBankListApi()
  if (res.code === 1) {
    bankList.value = res.data.user_bankcard
    // showSuccessToast(t('toastText.chenggong'))
  }
  console.log(res, 'bank')
}

onShow(() => {
  getBankLsit()
})

const goPage = () => {
  uni.navigateTo({ url: '/subPackages/bankAccount/accountLog' })
}
</script>

<style lang="scss" scoped>
* {
  font-weight: 500;
}
body {
  height: 100%;
}
.bg {
  width: 100%;
  // height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.inlet {
  height: 5rem;
  border-radius: 0.5rem;
  margin: 0.8125rem 0.94rem;
  background: #ffffff10;
  border: 0.06rem solid #ffffff16;
  text-align: center;
  .van-icon {
    width: 1.31rem;
    height: 1.31rem;
    margin-top: 1.19rem;
  }
  .label {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    color: $color-primary;
  }
}

.account-card-list {
  align-items: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .account-card {
    position: relative;
    width: 20.8125rem;
    height: 11.0313rem;
    padding-top: 1.4063rem;
    background: #ffffff;

    border-radius: 0.9375rem;
    margin-bottom: 0.8125rem;

    display: flex;
    justify-content: space-between;

    position: relative;

    .bank-account-number {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3.1563rem;
      background: #f8fafc;
      border-radius: 0 0 0.9375rem 0.9375rem;

      font-weight: 300;
      font-size: 1.3438rem;
      color: #010101;
      text-align: center;
      line-height: 3.1563rem;
    }

    .account-del-icon {
      margin-right: 1.0625rem;
      width: 0.7813rem;
      height: 0.9063rem;
    }

    .account-info {
      margin-left: 1.0625rem;
      z-index: 9;

      .bank-name {
        font-size: $uni-font-size-1;
      }
      .user-name {
        font-size: $uni-font-size-1;
      }
    }
  }
}

.card {
  height: 5.63rem;
  width: calc(100vw - 1.88rem);
  margin: 0 0.94rem;
  padding: 0.94rem 0.63rem;
  background: url('@/static/image/user/bank_bg.png');
  background-color: $color-primary;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 0.63rem;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  position: relative;
  color: #fff;
  .delete {
    width: 0.88rem;
    height: 0.88rem;
    position: absolute;
    right: 0.94rem;
    top: calc((5.63rem - 1rem) / 2);
  }
}
.card + .card {
  margin-top: 0.3rem;
}
</style>
