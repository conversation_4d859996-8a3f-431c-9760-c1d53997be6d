<script setup lang="ts">
import { reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { updatePasswordApi } from '@/api/changePassword'
import { showFailToast, showLoadingToast, showSuccessToast, showToast } from 'vant'
import icon1 from '@/static/1.png'
import icon2 from '@/static/2.png'
const form = reactive({
  old_password: '',
  new_Password: '',
  repeat_new_password: ''
})
const formList = reactive([
  {
    label: 'transactionPassions.jiumima',
    dataName: 'old_password',
    msgName: 'checkMsg.shurujiumima',
    isShow: false
  },
  {
    label: 'transactionPassions.xingmima',
    dataName: 'new_Password',
    msgName: 'checkMsg.shuruxingmima',
    isShow: false
  },
  {
    label: 'transactionPassions.xingmima2',
    dataName: 'repeat_new_password',
    msgName: 'checkMsg.shuruxingmima',
    isShow: false
  }
])
const { t } = useI18n()

const submit = async () => {
  showLoadingToast({ forbidClick: true, duration: 0 })
  if (form.old_password === '') {
    return showToast(t('changePassword.tip1'))
  }
  if (form.new_Password === '') {
    return showToast(t('changePassword.tip2'))
  }
  if (form.repeat_new_password === '') {
    return showToast(t('changePassword.tip2'))
  }
  if (form.new_Password.length < 6 || form.new_Password.length > 10) {
    return showToast(t('changePassword.tip3'))
  }
  if (form.new_Password !== form.repeat_new_password) {
    return showToast(t('changePassword.tip4'))
  }

  const res = await updatePasswordApi(form)
  if (res.code === 1) {
    showSuccessToast(res.msg)

    // 清除token 以及用户信息
    uni.setStorageSync('token', null)
    uni.setStorageSync('userId', null)
    uni.setStorageSync('userInfo', null)
    setTimeout(() => {
      // 提示用户
      showFailToast(t('login.user_info_null'))
      setTimeout(() => {
        // 跳转登录
        uni.navigateTo({
          url: '/subPackages/login/login'
        })
      }, 1500)
    }, 1500)
  }
}

const showPass = (i: number) => {
  formList[i].isShow = !formList[i].isShow
}
</script>

<template>
  <Navigater :title="t('user.rizhisuoyin')"></Navigater>

  <view class="Box">
    <!-- <view class="box-top"> </view> -->

    <view class="box-form-card">
      <!-- <view class="title">{{ t('changePassword.xiugaidenglumima') }}</view> -->
      <view v-for="(item, i) in formList" :key="i" class="box-form-item">
        <view class="box-form-item-label"> &nbsp;&nbsp;{{ t(item.label) }}</view>
        <van-field v-model="form[item.dataName]" :right-icon="item.isShow ? icon1 : icon2" :type="item.isShow ? 'text' : 'password'" :placeholder="t(item.msgName)" @click-right-icon="showPass(i)" />
        <!-- <img src="@/static/1.png" class="input-img" /> -->
      </view>
      <van-button class="btn" type="primary" @click="submit">{{ t('transactionPassions.queren') }}</van-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
::v-deep .van-field__control {
  height: 2rem;
  border: none;
  font-weight: 400;
  font-size: 0.9375rem;
  color: $color-white;
  &::placeholder {
    color: $color-gray;
  }
}
::v-deep .van-field {
  height: 3.375rem;
  background: transparent;
}
::v-deep .van-field__body {
  height: 3.375rem;
  background: #ffffff10;
  border: 0.06rem solid #ffffff16;
  border-radius: 0.5rem;
  padding: 1.0938rem 0.9375rem 0.9688rem 1.25rem;
  .van-field__right-icon {
    padding: 0;
    margin-right: 0.625rem;
    .van-icon__image {
      width: 1.19rem;
    }
  }
}

::v-deep .van-button {
  width: 100%;
  height: 2.75rem;
  background: $color-primary;
  border-radius: 1.13rem;
  font-size: 0.9375rem;
  font-weight: 500;
  margin: 2.5rem 1rem 0;
}

::v-deep .van-button__text {
  color: #fff;
  font-size: 0.9375rem;
  font-weight: 500;
}

::v-deep .van-field {
  padding: 0;
}

.Box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 1rem;

  .box-form-card {
    width: 100%;
    height: 31.9688rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      font-weight: 500;
      font-size: 1.1563rem;
      color: #040404;
      line-height: 1.1563rem;
      margin-bottom: 1.875rem;
    }

    .box-form-item {
      display: flex;
      flex-direction: column;
      margin-top: 0.9375rem;
      position: relative;
      width: 100%;

      .box-form-item-label {
        height: 2.75rem;
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 0.875rem;
        line-height: 0.75rem;
        color: $color-white;
      }
    }
  }

  .box-top {
    width: 11.4688rem;
    height: 11.4688rem;
    background: transparent;
    border-radius: 50%;

    font-weight: 300;
    font-size: 2.75rem;
    color: #cbcccd;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
