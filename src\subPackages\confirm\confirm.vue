<script setup lang="ts">
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
</script>

<template>
  <Navigater :title="t('user.benrenqueren')" />
  <view class="box">
    <view class="chatu"> 插图 </view>

    <view class="title"> 本人確認書類のご提出完了 </view>

    <view class="box-box"> </view>
    <view class="box-box"> </view>
  </view>
</template>

<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1.1875rem;

  .title {
    font-weight: 300;
    font-size: 0.8438rem;
    color: #040404;
    line-height: 2.0938rem;
    margin-top: 1.0938rem;
    margin-bottom: 0.3125rem;
  }

  .box-box {
    width: 19.8125rem;
    height: 2.5rem;
    border-radius: 1.2188rem;
    border: 0.0313rem solid #50aff0;
    margin-top: 1.0938rem;
  }

  .chatu {
    width: 21.75rem;
    height: 12.3438rem;

    font-weight: 300;
    font-size: 5.5rem;
    background: #fdfefe;
    color: #d0d3d5;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.625rem;
  }
}
</style>
