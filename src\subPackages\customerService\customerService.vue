<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import Navigator from '@/components/navigator/navigator.vue'

const { t } = useI18n()

const appId = ref(1)
const appInfo = ref({
  name: 'LINEAPP',
  tip: ''
})
const checkApp = (id: number) => {
  appId.value = id
}
</script>

<template>
  <Navigator :title="t('user.kefuzhongxin')" />
  <view class="box">
    <view class="title"> {{ t('customerService.title') }} </view>
    <view class="app-box">
      <view class="app-logo">logo</view>
      <view class="app-name">{{ appInfo.name }}</view>
      <view class="app-title">WD TREE</view>
    </view>
    <view class="app-card-box">
      <view class="app-card" @click="checkApp(1)">
        <view class="app-card-logo" :class="appId == 1 ? 'active' : ''"></view>
        <view class="app-card-tip">{{ t('customerService.shiyong') }}{{ appInfo.name }}</view>
        <view class="app-card-t">{{ t('customerService.tip') }}</view>
        <img src="@/static/image/customerService/right.png" class="app-card-right" />
      </view>
      <view class="app-card" @click="checkApp(2)">
        <view class="app-card-logo" :class="appId == 2 ? 'active' : ''"></view>
        <view class="app-card-tip">{{ t('customerService.shiyong') }}{{ appInfo.name }}</view>
        <view class="app-card-t">{{ t('customerService.tip2') }}</view>
        <img src="@/static/image/customerService/right.png" class="app-card-right" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
body {
  background: #ffffff;
}
.box {
  width: 100%;
  padding-top: 8.9688rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 1.2813rem 0 1.2813rem;

  .app-card-box {
    margin-top: 3.6563rem;
    // margin-bottom: 8.5938rem;
    display: flex;
    .app-card:nth-child(1) {
      margin-right: 0.5938rem;
    }
    .app-card {
      position: relative;
      display: flex;

      flex-direction: column;
      align-items: center;
      width: 10.0313rem;
      height: 14.1563rem;
      background: #e9f4fc;
      border-radius: 0.5938rem;
      opacity: 0.66;
      padding-top: 1.9063rem;

      .app-card-logo.active {
        background: #46abef;
      }

      .app-card-logo {
        width: 4rem;
        height: 4rem;
        background: #dde8f0;
        border-radius: 50%;
        border: 0.0313rem solid #dde8f0;
      }
      .app-card-tip {
        font-weight: 300;
        font-size: 0.9375rem;
        color: #050505;
        margin-top: 0.8438rem;
      }

      .app-card-t {
        font-weight: 300;
        font-size: $uni-font-size-1;
        color: #050505;
      }
      .app-card-right {
        width: 1.4063rem;
        // margin-top: 2.4063rem;
        position: absolute;
        bottom: 0.9375rem;
        left: 4.5938rem;
      }
    }
  }
  .app-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 3.25rem;

    .app-name {
      font-weight: 300;
      font-size: 1.25rem;
      color: #050505;
      font-size: 1.25rem;
      margin-top: 1.25rem;
      line-height: 1.25rem;
    }

    .app-title {
      font-weight: 300;
      font-size: 1rem;
      color: #050505;
      line-height: 1rem;
      margin-top: 0.4375rem;
    }

    .app-logo {
      width: 7.3438rem;
      height: 7.3438rem;
      background: #c6c8ca;
      border-radius: 50%;

      font-weight: 300;
      font-size: 1.9063rem;
      color: #050606;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .title {
    font-weight: 300;
    font-size: 1.25rem;
    color: #050606;
    line-height: 2.0938rem;
    span {
      color: #46abef;
    }
  }
}
</style>
