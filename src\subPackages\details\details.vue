<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getNewDetailApi } from '@/api/record/record'
import { newDetailType } from '@/api/record/recordType'
import { onLoad } from '@dcloudio/uni-app'
import { ref, Ref } from 'vue'
import { useCounterStore } from '@/store/store'
const { t } = useI18n()
const store = useCounterStore()
const newData: Ref<newDetailType> = ref({
  content: '',
  createtime: 0,
  id: 0,
  image: '',
  other_id: 0,
  title: '',
  updatetime: 0
})

const loadding = ref(true)

const getNewDetailFn = async (id: number | string) => {
  loadding.value = true
  const res = await getNewDetailApi(id)
  loadding.value = false
  if (res.code === 1) {
    newData.value = res.data
  }
}

// 格式化时间
function formatTimestamp(timestamp: number | string) {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  // 月份从0开始，所以我们需要加1
  const month = ('0' + (date.getMonth() + 1)).slice(-2)
  const day = ('0' + date.getDate()).slice(-2)
  const hours = ('0' + date.getHours()).slice(-2)
  const minutes = ('0' + date.getMinutes()).slice(-2)

  return `${year}/${month}/${day} ${hours}:${minutes}`
}

onLoad(({ id }) => {
  if (id) {
    getNewDetailFn(id)
  }
})
</script>
<template>
  <Navigater :title="t('details.xiaoxi')" />
  <van-loading v-if="loadding" type="spinner" style="text-align: center; margin-top: 20px" />
  <scroll-view v-else scroll-y :style="{ height: `calc(${store.pageHeight} - 3.125rem)` }">
    <view class="box">
      <view class="title-box">
        <view class="title">
          {{ newData.title }}
        </view>
        <view class="createtime">
          {{ formatTimestamp(newData.createtime * 1000) }}
        </view>
      </view>
      <view class="content-box">
        <rich-text :nodes="newData.content" style="color: #fff"></rich-text>
      </view>
    </view>
  </scroll-view>
</template>
<style lang="scss" scoped>
::v-deep.ma__p {
  color: $color-white;
}

.box {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.content-box {
  width: 100%;

  padding: 1.375rem 0.7188rem 6.25rem 1.875rem;
  font-size: 0.875rem;
}
.title-box {
  width: 20.9375rem;
  height: 6.25rem;
  padding: 1rem 0.625rem 0.9063rem 0.9375rem;
  border-bottom: 1px solid #afafaf;

  .title {
    font-weight: 500;
    font-size: 1rem;
    color: $color-white;
  }

  .createtime {
    margin-top: 0.3125rem;
    font-size: 0.875rem;
    color: $color-gray;
    text-align: right;
  }
}
</style>
