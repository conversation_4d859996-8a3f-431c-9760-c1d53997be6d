<template>
  <Navigater :title="t('ai.title')" />
  <scroll-view scroll-y :style="{ height: `calc(var(--vh) * 100 - 3.13rem ` }">
    <div class="record" @click="goPage('/subPackages/index/ai/record')">
      <img :src="record" alt="" />
      <span>{{ t('ai.record') }}</span>
    </div>
    <div class="logo">
      <img :src="logo" alt="" />
    </div>
    <div class="bottom">
      <div class="title">{{ t('ai.title') }}</div>
      <view class="input_wrap" @click="inputFocus = true">
        <input v-model="money" type="number" :placeholder="t('ai.placeholder')" :focus="inputFocus" @blur="inputFocus = false" />
      </view>
      <div class="button" @click="aiSubmit()">{{ t('ai.button') }}</div>
    </div>
  </scroll-view>
</template>

<script setup lang="ts">
import record from '@/static/image/index/record.png'
import logo from '@/static/image/index/ai_logo.png'
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import { goPage } from '@/common/common'
import { showLoadingToast, showToast } from 'vant'
import { aiSubmitApi } from '@/api/index'
const { t } = useI18n()
const money = ref(null)
const inputFocus = ref(false)

const aiSubmit = async () => {
  if (!money.value) {
    showToast(t('ai.placeholder'))
    return
  }
  showLoadingToast({ forbidClick: true, duration: 0 })
  const res = await aiSubmitApi({ money: money.value })
  if (res.code === 1) {
    showToast(res.msg)
    money.value = ''
  }
  console.log(res)
}
</script>

<style scoped lang="scss">
.record {
  display: flex;
  color: $color-gray;
  padding: 0.63rem 1.5rem 2.75rem;
  justify-content: flex-end;

  img {
    width: 1rem;
    object-fit: contain;
    gap: 0.31rem;
  }
}

.logo {
  display: flex;
  justify-content: center;
  img {
    width: 11.88rem;
    object-fit: contain;
  }
}

.bottom {
  padding: 0 1.5rem;
  .title {
    font-size: 1.13rem;
    color: #fff;
    font-weight: 500;
    margin: 1.13rem 0 0.63rem;
  }
  .input_wrap {
    height: 4.13rem;
    border-radius: 0.94rem;
    background: #ffffff10;
    border: 0.06rem solid #ffffff16;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 1.25rem;
    input {
      flex: 1;
    }
  }
  .button {
    height: 3.75rem;
    background: $color-primary;
    color: #fff;
    font-weight: 500;
    font-size: 0.94rem;
    border-radius: 0.94rem;
    margin-top: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
