<template>
  <Navigater :title="t('ai.record')" />
  <scroll-view scroll-y :style="{ height: `calc(var(--vh) * 100 - 3.13rem ` }">
    <div v-for="(item, index) in list" :key="index" class="block">
      <div class="top">
        <div class="tag" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</div>
        <div class="title">{{ item?.product?.name }}</div>
      </div>
      <div class="bottom">
        <div class="price">{{ changeMoney(item.submit_money) }}</div>
        <div class="num">
          <span>{{ t('ai.label') }}：</span>
          <span>{{ item.ai_order_sn }}</span>
        </div>
        <div class="time">{{ timestempToDate((item.createtime || 0) * 1000) }}</div>
      </div>
    </div>
    <NotData v-if="!list.length" />
  </scroll-view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getAiOrderApi } from '@/api/index'
import { timestempToDate, changeMoney } from '@/common/common'
const { t } = useI18n()

onShow(() => {
  getAiOrder()
})

const getAiOrder = async () => {
  const res = await getAiOrderApi()
  console.log(res)
  list.value = res.data
}

const list = ref([])
const statusMap = new Map([
  [1, t('ai.status1')],
  [2, t('ai.status2')],
  [3, t('ai.status3')]
])
const getStatusText = (status: number) => {
  return statusMap.get(status)
}
const getStatusClass = (status: number) => {
  return `status${status}`
}
</script>

<style scoped lang="scss">
.block {
  margin: 0 0.94rem;
  background: #1b2836;
  border-radius: 0.63rem;
  overflow: hidden;
  .top {
    display: flex;
    height: 1.63rem;
    align-items: center;
    color: #fff;
    justify-content: space-between;
    .tag {
      font-size: 0.75rem;
      padding: 0 0.63rem;
      height: 1.63rem;
      line-height: 1.63rem;
      width: 6.88rem;
    }
    .title {
      font-size: 0.94rem;
      padding: 0 0.63rem;
    }
    .status1 {
      background: linear-gradient(90deg, rgba(27, 114, 227, 0.6) 0%, rgba(27, 114, 227, 0) 100%);
    }
    .status2 {
      background: linear-gradient(90deg, rgba(0, 212, 50, 0.6) 0%, rgba(0, 212, 50, 0) 100%);
    }
    .status3 {
      background: linear-gradient(90deg, rgba(245, 63, 63, 0.6) 0%, rgba(245, 63, 63, 0) 100%);
    }
  }
  .bottom {
    padding: 0.63rem;
    height: 5.13rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .price {
      color: $color-primary;
      font-size: 0.94rem;
    }
    .num {
      color: $color-gray;
      font-size: 0.81rem;
    }
    .time {
      color: $color-gray;
      font-size: 0.69rem;
    }
  }
}
.block + .block {
  margin-top: 0.63rem;
}
</style>
