<template>
  <Navigater :title="t('gendanjilu.title')" />
  <scroll-view scroll-y :style="{ height: `calc(${store.pageHeight} - 3.125rem)` }">
    <view class="gupiao_list">
      <view v-for="(item, index) in gupiaoList" :key="index" class="gupiao">
        <view class="flex justify-between items-center">
          <view>
            <view class="text-left text-[1rem]">{{ item.product_name }}</view>
            <view class="text-lg">{{ t('gendanjilu.dingdanhao') }}：{{ item.order_sn }}</view>
          </view>
          <view class="button" @click="sell(item)">{{ t('gendanjilu.button') }}</view>
        </view>

        <view class="flex justify-between mt-[.5rem]">
          <view>
            <view class="text-red">{{ changeMoney(item.buy_in_money) }}</view>
            <view class="text-lg">{{ t('gendanjilu.mairujine') }}</view>
          </view>
          <view>
            <view class="text-red">{{ changeMoney(item.yesterday_profit) }}</view>
            <view class="text-lg">{{ t('gendanjilu.zuorishouyi') }}</view>
          </view>
          <view>
            <view class="text-red">{{ changeMoney(item.total_profit) }}</view>
            <view class="text-lg">{{ t('gendanjilu.zongshouyi') }}</view>
          </view>
        </view>

        <view class="flex pt-[0.4rem] mt-[0.4rem] bt">
          <view class="text-gray">{{ t('gendanjilu.zhuangtai') }}：</view>
          <view v-if="item.status === '1'">{{ t('gendanjilu.status1') }}</view>
          <view v-else-if="item.status === '2'">{{ t('gendanjilu.status2') }}</view>
          <view v-else-if="item.status === '3'">{{ t('gendanjilu.status3') }}</view>
        </view>

        <view class="block">
          <view>{{ t('gendanjilu.mairushijian') }}：{{ item.buying_time }}</view>
          <view v-show="item.end_time">{{ t('gendanjilu.maichushijian') }}：{{ item.end_time }}</view>
        </view>
      </view>
      <view v-if="gupiaoList.length === 0" class="nodata">{{ t('common.nodata') }}</view>
    </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { showConfirmDialog, showToast } from 'vant'
import { getGendanRecordListApi, sellGendanApi } from '@/api/index/index'
import { onLoad } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
import { changeMoney } from '@/common/common'
import { ref } from 'vue'
import { useCounterStore } from '@/store/store'
const store = useCounterStore()
const { t } = useI18n()

onLoad(() => {
  getGendanList()
})

// 獲取新股列表
const gupiaoList = ref([])
const getGendanList = async () => {
  const res = await getGendanRecordListApi()
  gupiaoList.value = res.data
}

// 賣出
const sell = async (e: any) => {
  console.log(e)
  showConfirmDialog({
    message: t('gendanjilu.window_title')
  }).then(async (res) => {
    if (res) {
      const res2 = await sellGendanApi({ follow_id: e.id })
      if (res2.code === 1) {
        showToast(res2.msg)
        getGendanList()
      }
    }
  })

  // buyParams.value.money = null
  // getGendanList()
  // uni.showToast({
  //   title: res.msg,
  //   icon: 'none'
  // })
}
</script>

<style scoped lang="scss">
.gupiao_list {
  .gupiao {
    margin: 0.52rem;
    padding: 0.52rem 0.9rem;
    border-radius: 0.62rem;
    background-color: #fff;
    text-align: center;
    .button {
      background-color: $uni-color-primary;
      color: #fff;
      height: 1.4rem;
      line-height: 1.4rem;
      padding: 0 0.7rem;
      border-radius: 0.3rem;
    }
    .bt {
      border-top: 0.03rem solid $uni-text-color-gray;
    }
    .block {
      background-color: $uni-bg-color-gray2;
      padding: 0.52rem;
      margin-top: 0.22rem;
      text-align: left;
      border-radius: 0.22rem;
    }
  }
}
</style>
