<template>
  <Navigater :title="t('jiaoyiDetail.title')" />
  <view class="info">
    <view class="flex justify-between">
      <view class="tt">{{ jiaoyiDetail?.product?.name }}</view>
      <view class="tt">{{ jiaoyiDetail?.yingsun }}</view>
    </view>
    <view class="flex">
      <view class="text-base">{{ jiaoyiDetail?.product?.shuzidaima }}</view>
      <view class="tag">{{ detailRows[0].value }}</view>
    </view>
    <view class="flex"></view>
  </view>
  <view class="rows">
    <view v-for="(item, index) in detailRows" v-show="!((item.key === 'selltime' || item.key === 'sellprice') && item.value === '-')" :key="index" class="row">
      <view class="label">{{ item.label }}</view>
      <view class="value" :class="getTextColor(item)">{{ item.key === 'yingkui' || item.key === 'sellprice' ? changeMoney(item.value) : item.value }}</view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue'
import { getOrderDetailApi } from '@/api/order'
import { onLoad } from '@dcloudio/uni-app'
import { changeMoney, timestampToDate3 } from '@/common/common'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const ismai = ref('1')
onLoad(async (options) => {
  ismai.value = options?.ismai
  console.log(ismai.value, 'options?.ismai')

  await getOrderDetailfn(options?.id)
})

const getTextColor = (item) => {
  if (item.key === 'yingkui' && Number(item.value) > 0) {
    return 'txt-green'
  } else if (item.key === 'yingkui' && Number(item.value) < 0) {
    return 'txt-red'
  } else {
    return 'text-ping'
  }
}

const jiaoyiDetail = ref({})
const getOrderDetailfn = async (id: string) => {
  const res = await getOrderDetailApi({ id, type: '1' })
  jiaoyiDetail.value = res.data
  for (const i in detailRows.value) {
    const key = detailRows.value[i].key
    for (const s in res.data) {
      if (key === s) {
        detailRows.value[i].value = res.data[s]
        break
      }
    }
    // 特殊处理
    if (key === 'sellprice') {
      detailRows.value[i].value = !Number(detailRows.value[i].value) ? '-' : detailRows.value[i].value
    }
    if (key === 'price') {
      detailRows.value[i].value = changeMoney(Number(detailRows.value[i].value))
    }

    if (key === 'buytime' || key === 'selltime') {
      if (key === 'selltime') {
        detailRows.value[i].value = detailRows.value[i].value ? timestampToDate3(detailRows.value[i].value * 1000) : '-'
      }
    } else if (key === 'shizhi') {
      detailRows.value[i].value = changeMoney(detailRows.value[i].value)
    }
  }
  console.log(res)
}

const tip1 = computed(() => (ismai.value === '1' ? t('jiaoyiDetail.shuliang1') : t('jiaoyiDetail.shuliang')))

const detailRows = ref([
  { label: t('jiaoyiDetail.leixing'), key: 'status_str', value: '' },
  { label: t('jiaoyiDetail.danhao'), key: 'order_sn', value: '' },
  { label: t('jiaoyiDetail.mairushijian'), key: 'buytime', value: '' },
  { label: t('jiaoyiDetail.mairujia'), key: 'price', value: '' },
  { label: t('jiaoyiDetail.maichushijian'), key: 'selltime', value: '' },
  { label: t('jiaoyiDetail.maichujia'), key: 'sellprice', value: '' },
  { label: tip1, key: 'shuliang', value: '' },
  { label: t('jiaoyiDetail.yingkui'), key: 'yingkui', value: '' },
  { label: t('jiaoyiDetail.mairuzongjia'), key: 'shizhi', value: '' },
  { label: t('jiaoyiDetail.ganggan'), key: 'ganggan_ratio', value: '' }
])
</script>
<style lang="scss" scoped>
.txt-green {
  color: $color-green !important;
}

.txt-red {
  color: $color-red !important;
}

.info {
  width: 20.9063rem;
  height: 3.9688rem;
  padding: 0.8rem 0.8rem;
  margin: 0.5rem auto 0;
  border-top-right-radius: 1.25rem;
  border-top-left-radius: 1.25rem;
  .tag {
    display: none;
    height: 1rem;
    border-radius: 0.1563rem;
    color: #fff;
    font-size: 0.625rem;
    padding: 0 0.375rem;
    margin-left: 0.3125rem;
  }
  .tt {
    color: $color-white;
    font-size: 1.19rem;
    font-weight: 500;
  }
  .text-base {
    font-size: 0.81rem;
    color: #afafaf;
    font-weight: 500;
  }
}
.rows {
  background-color: linear-gradient(168deg, #08182b 0.74%, #1b5191 99.61%);
  padding: 1.4375rem 1.875rem 0 2.1875rem;
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;

  .row {
    font-size: $uni-font-size-lg;
    height: 2.38rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .label {
      color: $color-gray;
      font-size: 0.875rem;
    }
    .value {
      color: $color-white;
      font-size: 0.875rem;
    }
  }
}
</style>
