<template>
  <view class="container">
    <view class="wrap">
      <img class="bg" :src="bg" />
      <view class="title">{{ t('login.tip3') }}</view>
      <view class="mas">
        <view class="input_wrap" @click="phoneFocus = true">
          <img :src="phone" />
          <input v-model="loginParams.account" :placeholder="t('login.account_placeholder')" :focus="phoneFocus" @blur="phoneFocus = false" />
        </view>
        <view class="input_wrap" @click="passwordFocus = true">
          <img :src="password" />
          <input v-model="loginParams.password" :placeholder="t('login.password_placeholder')" :type="showPassword ? 'text' : 'password'" :focus="passwordFocus" @blur="passwordFocus = false" />
          <van-icon :name="showPassword ? 'eye-o' : 'closed-eye'" color="#7B8A9C" size="1.3rem" @click.stop="showPassword = !showPassword" />
        </view>
      </view>
      <view class="button-wrap">
        <view class="button circle_button" @click="reLaunch('/subPackages/register/register')">{{ t('register.tip4') }}</view>
        <view class="button" @click="login">{{ t('login.button_text') }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { loginApi } from '@/api/login/login'
import { switchTab, reLaunch, checkInput } from '@/common/common'
import { useCounterStore } from '@/store/store'
import { showSuccessToast } from 'vant'
import bg from '@/static/image/login/logo.png'
import phone from '@/static/image/login/phone.png'
import password from '@/static/image/login/password.png'
import { onShow } from '@dcloudio/uni-app'
const { t } = useI18n()
const store = useCounterStore()

const loginParams = ref({
  account: '',
  password: ''
})

const showPassword = ref(false)
const phoneFocus = ref(false)
const passwordFocus = ref(false)

const checkArr = [
  { key: 'account', message: t('login.account_error') },
  { key: 'password', message: t('login.password_error') }
]

onShow(() => {
  loginParams.value.account = uni.getStorageSync('account') || ''
  loginParams.value.password = uni.getStorageSync('password') || ''
})

const login = async () => {
  if (!checkInput(checkArr, loginParams.value)) {
    return
  }
  uni.showLoading({ mask: true })
  const res = await loginApi(loginParams.value)
  uni.hideLoading()
  if (res.code === 1) {
    uni.setStorageSync('token', res.data.userinfo.token)
    uni.setStorageSync('userId', res.data.userinfo.id)
    uni.setStorageSync('userInfo', res.data.userinfo)
    uni.setStorageSync('account', loginParams.value.account)
    uni.setStorageSync('password', loginParams.value.password)
    store.token = res.data.userinfo.token
    store.userId = res.data.userinfo.userId
    store.getUserInfo()
    showSuccessToast(res.msg)
    setTimeout(() => {
      switchTab('/pages/index/index')
    }, 1000)
  }
}
</script>

<style lang="scss" scoped>
.navigator {
  width: 100%;
  height: 3.13rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem;
  .van-icon {
    transform: rotate(180deg);
  }
}
body {
  height: 100vh;
}

.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: calc(var(--vh) * 100);
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  .bg {
    width: 11.25rem;
    height: 11.25rem;
    margin: 2.25rem auto 0;
    border-radius: 0.63rem;
  }
  .title {
    color: $color-white;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 2rem 0 1.88rem;
  }
  .input_wrap {
    display: flex;
    align-items: center;
    height: 3.06rem;
    border-radius: 0.75rem;
    margin-bottom: 0.3rem;
    background: #ffffff10;
    border: 0.03rem solid #7b8a9c !important;
    padding: 0 0.88rem;
    max-width: 20rem;
    margin-left: auto;
    margin-right: auto;
    font-size: 1rem;
    gap: 1rem;
    input {
      flex: 1;
      text-align: left;
      .uni-input-placeholder {
        color: $color-gray;
      }
    }
    .pwd {
      padding: 0.2rem;
    }
    img {
      width: 1.5rem;
      height: 1.5rem;
      object-fit: contain;
    }
  }
}
.name {
  margin: 2.3vh auto 9.54vh;
  font-size: 1.5rem;
  color: $uni-color-primary;
}
.button-wrap {
  width: 20rem;
  margin-top: 1.875rem;
  display: flex;
  .button {
    width: 11.25rem;
    height: 3.06rem;
    border-radius: 2.63rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: $color-white;
    background: $color-primary;
    font-weight: 500;
    font-size: 1.13rem;
  }
  .circle_button {
    background: transparent !important;
    color: $color-primary;
    height: 3.06rem;
    flex: 1;
    display: flex;
    align-items: center;
  }
}
.mas {
  width: 20rem;
  height: 7.375rem;
  border-radius: 1.25rem;
}
</style>
