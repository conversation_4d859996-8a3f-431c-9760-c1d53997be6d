<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import { getUserMessageApi, getUserMessageDetailApi } from '@/api/message'
import { onLoad } from '@dcloudio/uni-app'
import { timestampToDate2 } from '@/common/common'
const { t } = useI18n()
const UserMessageList = ref([])
const messgaeInfo = ref({})
onLoad(() => {
  getUserMessage()
  getUserMessageDetail()
})

const params = ref({
  page: 1
})

const getUserMessage = async (type?) => {
  if (type === 'plus') {
    if (params.value.page < messgaeInfo.value.last_page) {
      params.value.page += 1
    } else {
      return
    }
  }

  const res = await getUserMessageApi(params.value)
  if (res.code === 1) {
    messgaeInfo.value = res.data
    if (params.value.page === 1) {
      UserMessageList.value = res.data.data
    } else {
      UserMessageList.value = [...UserMessageList.value, ...res.data.data]
    }
  }
}
const getUserMessageDetail = async () => {
  await getUserMessageDetailApi()
}
</script>
<template>
  <Navigater :title="t('message.xiaoxi')" />
  <view>
    <scroll-view scroll-y class="container" @scrolltolower="getUserMessage('plus')">
      <view v-for="(item, index) in UserMessageList" :key="index">
        <view class="createtime">{{ timestampToDate2(item.createtime) }}</view>
        <view class="content">{{ item.content }}</view>
      </view>
      <NotData v-if="UserMessageList.length === 0" />
    </scroll-view>
  </view>
</template>
<style lang="scss" scoped>
.container {
  height: calc(var(--vh) * 100 - 3.13rem);
  padding-bottom: 1rem;
}
.content {
  color: #fff;
  background-color: rgba(10, 31, 54, 0.8);
  margin: 0.625rem;
  padding: 0.9375rem 0.625rem;
  font-size: $uni-font-size-1;
  border-radius: 0.625rem;
}
.createtime {
  font-size: $uni-font-size-1;
  color: #747474;
  text-align: center;
  padding-top: 0.625rem;
}
</style>
