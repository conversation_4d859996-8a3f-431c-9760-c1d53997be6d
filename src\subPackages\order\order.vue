<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { getOrderDetailApi } from '@/api/order'
// import { paramsType } from '@/api/order/indexType'
import { onMounted, ref } from 'vue'
const { t } = useI18n()

const order = ref({})

const props = defineProps({
  id: {
    type: String
  },
  type: String
})

// 获取股票详情
const getOrderDetailFn = async () => {
  console.log(props.id)

  const res = await getOrderDetailApi(props)
  if (res.code === 1) {
    order.value = res.data
  }

  console.log(res)
}

onMounted(() => {
  getOrderDetailFn()
})
</script>
<template>
  <Navigater :title="t('order.tip1')" />
  <view class="box">
    <view class="title-box">
      <view class="name">{{ order?.product?.name }}</view>
      <view class="code">{{ order?.product?.shuzidai<PERSON> }}</view>
    </view>
    <van-cell-group class="order-box">
      <van-cell title="訂單號:" :value="order.order_sn" :arrow="false"></van-cell>
      <van-cell title="買入時間:" :value="order.buytime" :arrow="false"></van-cell>
      <van-cell title="賣出時間:" :value="order.selltime" :arrow="false" v-if="order.status === '2'"></van-cell>
      <van-cell title="買入價格:" :value="order.price" :arrow="false"></van-cell>
      <van-cell v-if="order.status === '2'" title="賣出價格:" :value="order.sellprice" :arrow="false"></van-cell>
      <van-cell title="持有數量:" :value="order.shuliang" :arrow="false"></van-cell>
      <van-cell title="原始保證金:" :value="order.benjin" :arrow="false"></van-cell>
      <van-cell title="槓桿倍數:" :value="order.ganggan_ratio" :arrow="false"></van-cell>
      <van-cell title="買入手續費:" :value="order.sxf_gyf" :arrow="false"></van-cell>
      <van-cell v-if="order.status === '2'" title="賣出手續費:" :value="order.sxf_pc" :arrow="false"></van-cell>
      <van-cell title="市值:" :value="order.shizhi" :arrow="false"></van-cell>
      <van-cell title="類型:" :value="order.buy_type == '0' ? '市價' : '限價'" :arrow="false"></van-cell>
      <van-cell v-if="order.status === '2'" title="證交稅:" :value="order.se_fee" :arrow="false"></van-cell>
      <van-cell v-if="order.status === '2'" title="證交稅:" :value="order.se_fee" :arrow="false"></van-cell>
      <van-cell title="狀態" :value="order.status_str" :arrow="false" class="zt"></van-cell>
    </van-cell-group>
    <!-- <view class="centen">
      <view class="row">
        <view class="leab">已实现损益</view>
        <view class="data">-309.92</view>
      </view>
    </view> -->
  </view>
</template>
<style lang="scss" scoped>
body {
  background: #fff;
}
.box {
  width: 100%;

  .centen {
    padding: 0 20px;
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .title-box {
    margin-top: 1.5625rem;
    margin-bottom: 1.5625rem;
    text-align: center;
    .name {
      font-weight: 600;
      font-size: 1.25rem;
    }

    .code {
      font-size: 1rem;
    }
  }
}
</style>
