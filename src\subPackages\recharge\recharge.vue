<script setup lang="ts">
import Navigator from '@/components/navigator/navigator.vue'
import { useCounterStore } from '@/store/store'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const store = useCounterStore()
const number = ref(0)
const moneyList = [1000000, 2000000, 5000000, 10000000, 20000000, 50000000]

const checkMoney = (item: number) => {
  number.value = item
}
</script>

<template>
  <Navigator />
  <view class="top-money"> {{ store.$state.userInfo?.money }} </view>
  <view class="recharge-box">
    <view class="recharge-box-title">{{ t('recharge.chongzhijine') }}</view>
    <!-- 允许输入数字，调起带符号的纯数字键盘 -->
    <van-field v-model="number" type="number" />
    <view class="money-tag-box">
      <view v-for="(item, i) in moneyList" :key="i" class="money-tag" :class="number == item ? 'active' : ''" @click="checkMoney(item)">
        {{ item.toLocaleString() }}
      </view>
    </view>

    <view class="sumbit">{{ t('recharge.queren') }}</view>
  </view>

  <view class="tip">{{ t('recharge.zhuyishixiang') }}:</view>
</template>
<style lang="scss" scoped>
.tip {
  margin-top: 0.7813rem;
  margin-left: 1.125rem;

  font-weight: 300;
  font-size: $uni-font-size-1;
  color: #292828;
}

.top-money {
  width: 100%;
  height: 4.5313rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f9fafb;
  margin-top: 0.4688rem;
  font-weight: 400;
  font-size: 1.5rem;
  color: #080707;
}

.recharge-box {
  width: 100%;
  height: 13rem;
  background: #f9fafb;
  margin-top: 0.4375rem;
  padding: 1.2813rem 1.2188rem 1.0938rem 1.1563rem;

  .sumbit {
    width: 21rem;
    height: 1.5938rem;
    margin-top: 0.5313rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #50b0f2;
    border-radius: 0.8125rem;

    font-weight: 300;
    font-size: $uni-font-size-1;
    color: #fefbfb;
  }

  .money-tag-box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 1.2188rem;

    .money-tag.active {
      background: #50aff0;
      color: #fbfcfc;
    }
    .money-tag {
      width: 6.3438rem;
      height: 1.5313rem;
      border-radius: 0.75rem;
      border: 0.0625rem solid #50b0f2;
      opacity: 0.59;

      font-weight: 500;
      font-size: $uni-font-size-1;
      color: #292828;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0.7188rem;
    }
  }

  .van-field {
    margin-top: 0.4688rem;
    width: 21.0625rem;
    height: 1.6563rem;
    background: #f9fafb;
    border-radius: 0.8125rem;
    border: 0.0625rem solid #50b0f2;
    padding: 0 0 0 0.625rem;
    display: flex;
    align-items: center;

    ::v-deep .van-field__control {
      line-height: 0;
    }
  }

  .recharge-box-title {
    font-weight: 300;
    font-size: $uni-font-size-1;
    color: #080707;
  }
}
</style>
