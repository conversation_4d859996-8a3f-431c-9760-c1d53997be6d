<template>
  <div class="container">
    <view class="top-box">
      <van-icon name="arrow-left" size="1.3rem" color="#fff" @click="goBack" />
      <view class="input_wrap">
        <img src="/static/image/index/search2.png" class="search-icon" />
        <input v-model="code" :placeholder="t('search.tip3')" auto-focus @confirm="search" />
      </view>
      <view class="search_text" @click="search">{{ t('search.title') }}</view>
    </view>
    <view class="content">
      <view v-if="!searchValue.length" class="keyword">
        <view v-for="item in history" :key="item" class="keyword-list">
          <view class="keyword-item" @click="clickSearch(item)">
            <view class="keyword-item-left">{{ item }}</view>
            <image src="/static/image/searchNavigation/close.png" class="keyword-close" @click.stop="delkeyword(item)" />
          </view>
        </view>
      </view>
      <view class="list">
        <NotData v-if="!searchValue.length" />
        <view v-for="item in searchValue" :key="item.id" class="list-item" @click="goPage(item.id)">
          <view class="item-title">
            <span class="title">{{ item.name }} {{ item.shuzidaima }} </span>
            <span :class="getColor(item)">{{ item.price }}&nbsp; {{ item.zhangdieshu }}({{ item.zhangdiebaifenbi }}%)</span>
          </view>
          <van-icon name="arrow" color="#fff"></van-icon>
        </view>
      </view>
    </view>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { searchStock } from '@/api/search'
import { onLoad } from '@dcloudio/uni-app'
import { closeToast, showLoadingToast } from 'vant'
// import icon1 from '@/static/arrow-left.png'
import { getColor } from '@/common/common'

const { t } = useI18n()
const code = ref('')
const history = ref([])
const searchValue = ref([])

const search = async () => {
  showLoadingToast({
    message: t('search.tip2'),
    forbidClick: true,
    overlay: true
  })
  const res = await searchStock({ code: code.value })
  closeToast()
  if (res.code === 1) {
    if (res.data.result.length) {
      if (!history.value.find((item) => item === code.value)) {
        addHistory()
        getHistory()
      }
    }
    searchValue.value = res.data.result
  }
}

const clickSearch = (value) => {
  code.value = value
  search()
}

// 添加搜索历史
const addHistory = () => {
  history.value.push(code.value)
  console.log(history.value, 'history.value')

  uni.setStorageSync('history', history.value)
}

// 获取搜索历史
const getHistory = () => {
  history.value = uni.getStorageSync('history') || []
}

// 跳转详情
const goPage = (id: number) => {
  uni.navigateTo({
    url: '/subPackages/index/gupiaoDetail/gupiaoDetail?id=' + id
  })
}

const goBack = () => {
  uni.navigateBack({})
}

// 删除记录
const delkeyword = (value) => {
  history.value = history.value.filter((item) => item !== value)
  uni.setStorageSync('history', history.value)
}

onLoad(() => {
  getHistory()
})
</script>

<style lang="scss" scoped>
.container {
  overflow: hidden;
}
.uni-input-placeholder {
  font-size: 0.8125rem !important;
}

.nav {
  width: 100%;
  height: 3.125rem;
}
.top-box {
  width: 100%;
  height: 3.125rem;
  display: flex;
  align-items: center;
  margin: 0.53rem 0;
  gap: 1rem;
  .search {
    width: 4.375rem;
    height: 2rem;
    margin-left: 0.625rem;
    margin-right: 0.9375rem;
  }
  .search_text {
    color: $color-white;
    font-size: 0.875rem;
    padding-right: 1rem;
  }
  .van-icon {
    padding-left: 1rem;
  }
  .input_wrap {
    width: 14.375rem;
    flex: 1;
    height: 2.75rem;
    border-radius: 0.63rem;
    background: #ffffff10;
    border: 0.03rem solid ffffff16;
    line-height: 2.75rem;
    padding: 0 0.94rem;
    font-size: 0.9375rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    .search-icon {
      width: 1rem;
      height: 1rem;
    }
    .input-placeholder {
      color: #aeaeae;
    }
  }
}
.content {
  width: 100%;

  height: calc(var(--vh) * 100 - 3.125rem - 1.06rem);
  overflow: auto;
  .list {
    .not-data {
      text-align: center;
      margin-top: 6.25rem;
      color: #0e0e0e;
    }
    .list-item {
      height: 4.0625rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 1rem;
      border-bottom: 0.06rem solid #aeaeae;
      .arrow {
        width: 1.25rem;
        height: 1.25rem;
      }

      .item-title {
        height: 4.0625rem;
        font-weight: 600;
        font-size: $uni-font-size-lg;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        justify-content: center;

        span {
          font-size: $uni-font-size-lg;
          font-weight: 400;
          margin-top: 0.3125rem;
        }
        .title {
          color: $color-white;
        }
      }
    }
  }

  .keyword {
    margin-top: 0.4375rem;
    display: flex;
    flex-flow: wrap;
    font-size: $uni-font-size-2;
    .keyword-item {
      width: 23.4375rem;
      height: 2.125rem;
      padding: 0.5rem 0.9375rem 0.4063rem 0.9375rem;
      height: 1.7188rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .keyword-item-left {
        color: #a6a6a6;
        font-size: 0.875rem;
      }

      .keyword-close {
        width: 0.6995rem;
        height: 0.6995rem;
      }
    }
  }
}

::v-deep.uni-input-input {
  font-size: 0.82rem !important;
}

uni-input {
  flex: 1;
}
</style>
