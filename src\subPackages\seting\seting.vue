<script setup lang="ts">
// import { Ref, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCounterStore } from '@/store/store'
const store = useCounterStore()
const { t } = useI18n()
const goPage = (url: string) => {
  uni.navigateTo({ url })
}

const logout = () => {
  console.log('logout!!')
  console.log(store.clearState, 'store.clearState!!')
  store.clearState()
}
</script>
<template>
  <Navigater :title="t('seting.sheding')" />
  <view class="card">
    <view class="form-item">
      <view class="form-title">{{ t('seting.xingming') }}</view>
      <view class="form-val">{{ store.$state.userInfo?.true_name || '' }}</view>
    </view>
    <view class="form-item">
      <view class="form-title">{{ t('seting.zhanghu') }}</view>
      <view class="form-val">{{ store.$state.userInfo?.username || '' }}</view>
    </view>
    <view class="form-item" @click="goPage('/subPackages/changePassword/changePassword')">
      <view class="form-title">{{ t('seting.denglumima') }}</view>
      <van-icon name="arrow" color="#fff"></van-icon>
    </view>
    <view class="form-item" @click="goPage('/subPackages/transactionPassions/transactionPassions')">
      <view class="form-title">{{ t('seting.zijinmima') }}</view>
      <van-icon name="arrow" color="#fff"></van-icon>
    </view>

    <view class="logout" @click="logout">
      {{ t('seting.logout') }}
    </view>
  </view>
</template>
<style lang="scss" scoped>
.card {
  width: 100%;
  height: 14.6563rem;
  // background: #ffffff;
  margin-top: 0.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;

  .logout {
    width: 21rem;
    height: 2.75rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.875rem;
    background: $color-primary;
    border-radius: 1.13rem;

    font-weight: 300;
    font-size: 0.9375rem;
    color: #fff;
  }

  .form-item {
    width: 100%;
    height: 2.125rem;
    padding: 0 1.1875rem 0 1.1875rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-bottom: 0.06rem solid #adc3d1;

    .form-title {
      font-weight: 300;
      font-size: $uni-font-size-2;
      color: $color-white;
    }

    .form-val {
      font-weight: 300;
      font-size: $uni-font-size-2;
      color: $color-white;
    }

    img {
      height: 0.9688rem;
    }
  }
}
</style>
