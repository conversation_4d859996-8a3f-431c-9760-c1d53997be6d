<script setup lang="ts">
import { computed, Ref, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

type itmeType = {
  name: string
  val: string
  isShow: boolean
}
type listType = [itmeType]
const check1 = computed(() => locale.value === 'cn')
const check2 = computed(() => locale.value === 'jp')
const check3 = computed(() => locale.value === 'en')
const check4 = computed(() => locale.value === 'sp')
const check5 = computed(() => locale.value === 'fr')

const list: Ref<listType> = ref([
  // {
  //   name: 'yuyan.zhongwenjianti',
  //   val: 'cn',
  //   isShwo: check1
  // },
  {
    name: 'yuyan.ribenyu',
    val: 'jp',
    isShwo: check2
  },
  {
    name: 'yuyan.yinyu',
    val: 'en',
    isShwo: check3
  },
  {
    name: 'yuyan.xibanyayu',
    val: 'sp',
    isShwo: check4
  },
  {
    name: 'yuyan.fayu',
    val: 'fr',
    isShwo: check5
  }
])

const checkLocale = (val: string) => {
  uni.setStorageSync('locale', val)
  locale.value = val
  console.log(check1.value, 'check1')
  console.log(check2.value, 'check2')
  console.log(check3.value, 'check3')
  console.log(check4.value, 'check4')
  console.log(check5.value, 'check5')
}
</script>
<template>
  <Navigater :title="t('user.qiehuanyuyan')" />
  <view class="box">
    <view class="title">
      <img src="/static/image/user/language.png" />
      <span>{{ t('user.qiehuanyuyan') }}</span>
    </view>
    <view class="list">
      <view v-for="(item, i) in list" :key="i" class="item" @click="checkLocale(item.val)"
        ><span :class="{ check: item.val === locale }">{{ t(item.name) }}</span>
        <img v-if="item.val === locale" src="/static/image/user/checked.png" />
        <img v-else src="/static/image/user/circle.png" />
      </view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
.box {
  padding: 0 1.25rem;
}

.title {
  margin-top: 0.9375rem;
  display: flex;
  align-items: center;
  img {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.625rem;
  }
  span {
    color: $color-black;
    font-size: 1rem;
    font-weight: 500;
  }
}

.list {
  // background: #08182b;
  margin-top: 0.9063rem;
  .item {
    width: 100%;
    height: 3.125rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.56rem;

    align-items: center;
    border-bottom: 0.06rem solid #c8ccd2;

    .link {
      width: 18.4375rem;
      height: 0.0313rem;
    }
    span {
      font-weight: 400;
      font-size: 0.875rem;
      color: $color-gray;
    }
    img {
      width: 1rem;
    }
    .check {
      color: $color-black;
    }
  }
}
</style>
