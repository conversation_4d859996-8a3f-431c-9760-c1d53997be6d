<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { CzType } from '@/api/transactionLog/indexType'
import { changeMoney } from '@/common/common'
import { computed, onMounted, ref } from 'vue'
interface PropsType {
  data: Array<CzType>
  value: Number
}
onMounted(() => {
  console.log(props.data)
})
const props = defineProps<PropsType>()
const { t } = useI18n()
const emit = defineEmits(['bottom'])
// const shenhe_str = ref([t('incomeLog.daishenhe'), t('incomeLog.yipizhun'), t('incomeLog.shenheshibai')])
const text1 = computed(() => t('transactionLog.tip2'))
const text2 = computed(() => t('transactionLog.tip3'))
const text3 = computed(() => t('transactionLog.tip4'))
const remark = ref([text1, text2, text3])
const bottomFn = () => {
  emit('bottom')
}
</script>
<template>
  <scroll-view scroll-y :style="{ height: `calc(var(--vh) * 100 - 7.5rem)`, overflow: 'hidden' }" @scrolltolower="bottomFn">
    <NotData v-if="data.length === 0" />
    <view class="list-box">
      <view v-for="item in props.data" :key="item.id" class="list-item">
        <div class="flex">
          <div class="left">
            <view class="list-item-shouru" style="color: #fff; font-size: 0.9375rem; font-weight: 500">{{ props.value === 1 ? t('incomeLog.shourujilu') : t('incomeLog.zhichujilu') }}</view>
            <view class="list-item-time" style="margin: 0.3rem 0">{{ t('incomeLog.dingdanbianhao') }}:{{ item.order_sn }}</view>
            <view class="list-item-time" style="text-align: left">{{ item.createtime }}</view>
          </div>
          <div class="right">
            <view class="list-item-shouru">{{ changeMoney(item.money) }}</view>
            <view v-if="props.value === 2" class="list-item-tag">{{ remark[Number(item.status)].value }}</view>
          </div>
        </div>
      </view>
    </view>
  </scroll-view>
</template>
<style lang="scss" scoped>
.list-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.25rem 0;

  .list-item {
    width: 21.5625rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0.625rem 0.625rem;
    margin-bottom: 0.4375rem;
    border-bottom: 0.06rem solid #c8c8c8;
    .flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .right {
        text-align: right;
      }
    }

    .list-item-left {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .list-item-right {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .list-item-tag {
      padding: 0.0938rem 0.25rem;
      margin-top: 0.3125rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $color-red;
      border-radius: 1.25rem;

      font-weight: 500;
      font-size: 0.75rem;
      color: #fff;
    }

    .list-item-shouru {
      font-weight: 500;
      font-size: 0.9375rem;
      font-weight: 600;
      color: $color-green;
    }

    .list-item-time {
      color: $color-gray;
      text-align: right;
      font-size: 0.8125rem;
      line-height: 1.125rem;
    }
  }
}
</style>
