<script setup lang="ts">
import { ref, Ref, reactive, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { PayPasswordType } from '@/api/transactionPassions/indexType'
import { setPayPasswordApi } from '@/api/transactionPassions'
import { useCounterStore } from '@/store/store'
import { showLoadingToast, showSuccessToast, showToast } from 'vant'
import { getWithdrawal } from '@/api/user'
import { onLoad } from '@dcloudio/uni-app'
import icon1 from '@/static/1.png'
import icon2 from '@/static/2.png'

const store = useCounterStore()
const form: Ref<PayPasswordType> = ref({
  new_pay_password: '',
  old_pay_password: '',
  re_pay_password: '',
  user_id: store.$state.userId
})
const paypassword = ref('')

onLoad(() => {
  getWithdrawalFn()
})

const getWithdrawalFn = async () => {
  const res = await getWithdrawal(store.$state.userId)
  if (res.code === 1) {
    paypassword.value = res.data.user_info.paypassword
    // paypassword.value = ''
    console.log(paypassword.value, 'vvvv')
  }
  console.log(res)
}
const isIf = computed(() => {
  return paypassword.value === '' ? null : true
})

const formList = reactive([
  {
    label: 'transactionPassions.jiumima',
    dataName: 'old_pay_password',
    msgName: 'checkMsg.shurujiumima',
    isShow: false,
    isIf: isIf
  },
  {
    label: 'transactionPassions.xingmima',
    dataName: 'new_pay_password',
    msgName: 'checkMsg.shuruxingmima',
    isShow: false,
    isIf: true
  },
  {
    label: 'transactionPassions.xingmima2',
    dataName: 're_pay_password',
    msgName: 'checkMsg.shuruxingmima',
    isShow: false,
    isIf: true
  }
])
const { t } = useI18n()

const showPass = (i: number) => {
  formList[i].isShow = !formList[i].isShow
}

const submit = async () => {
  showLoadingToast({ forbidClick: true, duration: 0 })
  if (paypassword.value !== '') {
    if (form.value.old_pay_password === '') {
      return showToast(t('transactionPassions.tip1'))
    }
  }

  if (form.value.new_pay_password === '') {
    return showToast(t('transactionPassions.tip2'))
  }
  if (form.value.re_pay_password === '') {
    return showToast(t('transactionPassions.tip2'))
  }
  if (form.value.new_pay_password.length < 6 || form.value.new_pay_password.length > 10) {
    return showToast(t('transactionPassions.tip3'))
  }
  if (form.value.new_pay_password !== form.value.re_pay_password) {
    return showToast(t('transactionPassions.tip4'))
  }

  const res = await setPayPasswordApi(form.value)
  if (res.code === 1) {
    showSuccessToast(res.data.msg)
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/user/user'
      })
    }, 1000)
  }
}
</script>

<template>
  <Navigater :title="t('user.shiwuchuli')"></Navigater>

  <view class="Box">
    <view class="box-form-card">
      <view v-for="(item, i) in formList" v-show="item.isIf" :key="i" class="box-form-item">
        <view class="box-form-item-label"> &nbsp;&nbsp;{{ t(item.label) }}</view>
        <van-field v-model="form[item.dataName]" :right-icon="item.isShow ? icon1 : icon2" :type="item.isShow ? 'text' : 'password'" :placeholder="t(item.msgName)" @click-right-icon="showPass(i)" />
      </view>
      <van-button class="btn" type="primary" @click="submit">{{ t('transactionPassions.queren') }}</van-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
::v-deep .van-field__control {
  height: 2rem;
  border: none;
  font-weight: 400;
  font-size: 0.9375rem;
  color: $color-white;
  &::placeholder {
    color: #afafaf;
  }
}
::v-deep .van-field {
  height: 3.375rem;
  background: transparent;
}
::v-deep .van-field__body {
  height: 3.375rem;
  background: #ffffff10;
  border: 0.06rem solid #ffffff16;
  border-radius: 0.5rem;
  padding: 1.0938rem 0.9375rem 0.9688rem 1.25rem;
  .van-field__right-icon {
    padding: 0;
    margin-right: 0.625rem;
    .van-icon__image {
      width: 1.19rem;
    }
  }
}

::v-deep .van-button {
  width: 100%;
  height: 2.75rem;
  background: $color-primary;
  border-radius: 1.13rem;
  font-size: 0.9375rem;
  font-weight: 500;
  margin: 2.5rem 1rem 0;
}

::v-deep .van-button__text {
  color: #fff;
  font-size: 0.9375rem;
  font-weight: 500;
}

::v-deep .van-field {
  padding: 0;
}

.Box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 1rem;

  .box-form-card {
    width: 100%;
    height: 31.9688rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      font-weight: 500;
      font-size: 1.1563rem;
      color: #040404;
      line-height: 1.1563rem;
      margin-bottom: 1.875rem;
    }

    .box-form-item {
      display: flex;
      flex-direction: column;
      margin-top: 0.9375rem;
      position: relative;
      width: 100%;

      .box-form-item-label {
        height: 2.75rem;
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 0.875rem;
        line-height: 0.75rem;
        color: $color-white;
      }
    }
  }

  .box-top {
    width: 11.4688rem;
    height: 11.4688rem;
    background: transparent;
    border-radius: 50%;

    font-weight: 300;
    font-size: 2.75rem;
    color: #cbcccd;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
