import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'
import eslintPlugin from 'vite-plugin-eslint'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
    eslintPlugin({
      cache: false
    })
  ],
  server: {
    host: '0.0.0.0',
    port: 8080, // 端口号
    hmr: true,
    open: false, // 启动后是否自动打开浏览器
    proxy: {
      '/api': {
        target: 'http://*************:9906', // 目标源，目标服务器，真实请求地址
        // target: 'https://admin.backendshtllc.com/', // 目标源，目标服务器，真实请求地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api') // 重写真实路径,替换/api
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
        @use "@/style/output.scss";
        `,
        silenceDeprecations: ['legacy-js-api']
      }
    }
  }
})
